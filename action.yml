name: ras cumulus pipeline parameters action
description: sets up pathing required to pass into merge util

inputs:
  PROJECT_CONFIG_PATH:
    description: 'example: project-configs/a208203-events/billing-grouper.yaml'
    required: true

runs:
  using: composite
  steps:
    - name: set action path as output from this step
      id: setup-action-path
      run: |
        echo "::group::logging useful output when setting up action path for parameters repo"
        pwd
        ls -l
        cd ${{ github.action_path }}
        pwd
        ls -l
        echo "::set-output name=action-path::${{ github.action_path }}"
        echo "you didn't see anything... RIGHT!"
        echo "::endgroup::"
      shell: sh

    - name: github action clone hack for cumulus templates repo
      id: github-clone-hack-templates
      uses: tr/cars-shared_pipeline-templates@main

    # Since projects still run the merge util with a specific project config we still need to leave this action alone
    # which allows the current implementation to still work while we migrate to the c5s pipeline approach. - <PERSON>
    - name: run the new cumulus template replace action
      # TODO: once we get everyone set up we could ping tr/cars-shared_cumulus-template-replace-action@main instead
      uses: tr/ras_cumulus-template-replace-action@main-v2
      id: generic-cumulus-template-merge
      with:
        TEMPLATES_REPO_ACTION_PATH: ${{ steps.github-clone-hack-templates.outputs.TEMPLATES_REPO_ACTION_PATH }}
        PARAMETERS_REPO_ACTION_PATH: ${{ steps.setup-action-path.outputs.action-path }}
        PROJECT_CONFIG_PATH: ${{ inputs.PROJECT_CONFIG_PATH }}
