name: TrOrg-CloudIaC-ProjectCi

on:
  push:
    branches:
      - '**'
    paths-ignore:
      - 'docs/**'
      - '**.md'
    tags-ignore:
      - '**'
  workflow_dispatch:
    inputs:
      buildType:
        description: Type of build/release
        required: true
        type: choice
        options:
          - dev
          - hotfix

env:
  ENV_CONFIG_FILE: ".github/env-variables.txt"
  SPECTRAL_DSN: ${{ secrets.A208375_SPECTRAL_DSN }}

jobs:
  # CI Build Job
  build:
    if: github.event_name != 'pull_request'
    runs-on: ubuntu-latest
    permissions: write-all
    outputs:
      ENVIRONMENT_VARIABLE_PATH: ${{ env.ENV_CONFIG_FILE }}
      BRANCH_RELEASE_TAG: ${{ steps.get_version.outputs.BRANCH_RELEASE_TAG }}

    steps:
      # Drafts your next Release notes as Pull Requests are merged into "main"
      - name: Create draft release
        id: draft-release
        if: github.ref == 'refs/heads/main'
        uses: release-drafter/release-drafter@3f0f87098bd6b5c5b9a36d49c41d998ea58f9348 # v6.0.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
        with:
          fetch-depth: 0
          ref: ${{ github.ref_name }}

      - name: github action clone hack for cumulus templates repo
        id: github-clone-hack-templates
        uses: tr/cars-shared_pipeline-templates@main

      - name: run the c5s cumulus template replace action
        uses: tr/cars-shared_cumulus-template-replace-action@main
        id: c5s-cumulus-template-merge
        with:
          # template repo action path still needed for the c5s flag
          TEMPLATES_REPO_ACTION_PATH: ${{ steps.github-clone-hack-templates.outputs.TEMPLATES_REPO_ACTION_PATH }}
          # since this isn't an action file we need to use the workspace instead of the action-path
          PARAMETERS_REPO_ACTION_PATH: ${{ github.workspace }}
          # since we are running the c5s flag we don't care about this but its required so we pass a fake value
          PROJECT_CONFIG_PATH: project-configs/asset-folder/project-yaml-file.yaml
          # required to generate all the files for each project
          PREP_PAAS_IAC_INSTALL_FLAG: true

      - name: "Get version"
        shell: sh
        if: ${{ steps.draft-release.outputs.tag_name != null }}
        run: |
          tag=${{ steps.draft-release.outputs.tag_name }}
          echo TAG=$tag >> $GITHUB_ENV
          echo VERSION=${tag#v} >> $GITHUB_ENV
          # create VERSION file with version (will be used by the deploy process)
          echo ${tag#v} > VERSION

      - name: "Get version"
        id: get_version
        if: (github.event.inputs != '')
        shell: sh
        run: |
          latest_tag=$(git describe --tags --abbrev=0 | cut -d "-" -f 1)
          if ${{ github.event.inputs.buildType == 'hotfix' }}
          then
            tag="$latest_tag-hotfix"
          else
            short_sha=$(git rev-parse --short "${{ github.sha }}")
            tag="$latest_tag-dev.$short_sha"
          fi
          echo TAG=$tag >> $GITHUB_ENV
          echo "BRANCH_RELEASE_TAG=$tag" >> $GITHUB_OUTPUT
          echo VERSION=${tag#v} >> $GITHUB_ENV
          # create VERSION file with version (will be used by the deploy process)
          echo ${tag#v} > VERSION

      - name: Set environment variables defined in the ENV_CONFIG_FILE
        shell: bash
        run: |
          IFS="["
          while read -r -a LINE || [ -n "$LINE" ] ; do
            if [[ -n "$LINE" && ${LINE::1} != "#" ]]; then
              echo "$LINE" | xargs;
            fi
          done < ${{ env.ENV_CONFIG_FILE }} >> $GITHUB_ENV

      - name: Validate Cloudformation, Yaml, Python, and CDK templates (if present) with pytest
        continue-on-error: false
        env:
          ARTIFACTORY_TOKEN: ${{ secrets[env.ARTIFACTORY_TOKEN_SECRET_NAME] }}
          ARTIFACTORY_USER: ${{ env.ARTIFACTORY_USER }}
        run: |
          python3 -m venv .venv
          source .venv/bin/activate

          # if a requirements.txt file exists, install required libraries
          if [ -e requirements.txt ]
          then
            echo "requirements.txt file found - installing libraries..."
            pip install -r requirements.txt --extra-index-url "https://${{ env.ARTIFACTORY_USER }}:${{ env.ARTIFACTORY_TOKEN }}@tr1.jfrog.io/tr1/api/pypi/pypi-local/simple"
          fi
          pip3 install cfn-lint yamllint pylint pytest

          # if iac is using shared workflows, copy the templates from artifactory to the templates directory
          if [ -d "/home/<USER>/.local/cfn-templates" ] && ! [ -d "templates" ]
          then
            mkdir templates && cp -R /home/<USER>/.local/cfn-templates/* templates
          elif [ -d "/home/<USER>/.local/cfn-templates" ] && [ -d "templates" ]
          then
            cp -n /home/<USER>/.local/cfn-templates/* templates
          fi

          # if yaml files exist in /templates directory, run cfn-lint
          yamlcount=`find templates -type f -name '*.yaml' | wc -l`
          if [ $yamlcount != 0 ]
          then
            echo "Yaml files found under the /templates directory - running cfn-lint..."
            cfn-lint templates/**/*.yaml -i W
            echo "Yaml cloudformation templates successfully validated"
          fi

          echo "Running yamllint on the /config directory"
          yamllint -d "{extends: relaxed, rules: {line-length: {max: 300}}}" config
          echo "Yaml files successfully validated"

          # if python files exist in /templates directory, run pylint
          pycount=`find templates -type f -name '*.py' | wc -l`
          if [ $pycount != 0 ]
          then
            echo "Python files discovered in the /templates directory - running pylint..."
            pylint ./templates --disable=R,C,W
            echo "Python files successfully validated."
          fi

          # if test directory exists, run pytest on any test files present in that directory
          if [ -d "test" ]
          then
            echo "/test directory is present - running pytest..."
            cd test
            pytest .
            echo "testing complete."
          fi

         # Run devops_lead-time-action
      - name: Run devops_lead-time-action
        uses: tr/devops_lead-time-action@c525d4b6b00ccadb16c99a44293825b608820755 # v1.0.10
        id: lead-time
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_WORKSPACE: ${{ github.workspace }}

      - name: fetch repo topics
        shell: sh
        run: |
          gh_resp=$(gh api -H "Accept: application/vnd.github+json" /repos/${{ github.repository }}/topics)
          echo REPO_TOPICS=$(echo "$gh_resp" | jq '.names') >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check repo properties
        shell: bash
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          # Get all repo properties and iterate through them
          echo $(gh api /repos/$GITHUB_REPOSITORY/properties/values) | jq -c '.[]' | while read i; do
            # If the property name is "fedramp-scoped" then echo the key and value into GitHub Actions environment variables
            if [[ $(echo $i | jq -r '.property_name') == "fedramp-scoped" ]]; then
              echo "Setting FEDRAMP_SCOPED environment variable"
              echo FEDRAMP_SCOPED=$(echo $i | jq -r '.value') >> $GITHUB_ENV
            fi
          done

      - name: Create and upload release-info.json
        shell: sh
        if: github.ref == 'refs/heads/main'
        id: upload-release-info
        run: |
          echo '{"service_name":"${{ env.SERVICE_NAME }}", "fedramp-scoped":"${{ env.FEDRAMP_SCOPED }}", "github_repository":"${{ github.repository }}", "github_sha": "${{ steps.lead-time.outputs.commit }}", "repo_topics":${{ env.REPO_TOPICS }}, "full_semver": "${{ steps.lead-time.outputs.name }}", "created_at": "${{ steps.lead-time.outputs.time }}", "lead_time_to_release_days": "${{ steps.lead-time.outputs.lead_time }}"}' > release-info.json

      - name: Install and run Spectral CI
        uses: spectralops/spectral-github-action@e5c74cf93c5dfc163527851a1e907f4e7d956d4e # v5.0.0
        with:
          spectral-dsn: ${{ env.SPECTRAL_DSN }}
          spectral-args: scan --ok  --include-tags iac

      # I added some exclusions to the zip command to simplify the contents of the artifact being sent to c5s pipeline - Adam Peterson
      - name: Zip project
        run: |
          zip -r artifact.zip * -x ".github/*" "action.yml" "action-c5s.yml" "project-configs/*"

      # Required for Attestation Generation Job below
      - name: Upload Artifact
        if: (github.ref == 'refs/heads/main') || (github.event.inputs != '')
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 #v4.6.2
        with:
          name: artifact.zip
          path: "${{ github.workspace }}/artifact.zip"

      - name: Upload Release Asset
        shell: sh
        if: github.ref == 'refs/heads/main'
        id: upload-release-asset
        run: |
          gh release upload --clobber $TAG ./artifact.zip
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create Dev/Hotfix draft release
        if: (github.event.inputs != '')
        uses: ncipollo/release-action@2c591bcc8ecdcd2db72b97d6147f871fcd833ba5 # v1.14.0
        with:
          allowUpdates: true
          tag: ${{ env.TAG }}
          artifacts: ./artifact.zip
          draft: true
          commit: ${{ github.sha }}
          token: ${{ secrets.GITHUB_TOKEN }}

  # Calling  workflow "TrOrg-Attestation-Generation" for FedRAMP compliance
  generate_build_attestation:
    if: (github.ref == 'refs/heads/main') || (github.event.inputs != '')
    needs: [ build ]
    uses: tr/reuseable-github-actions_attestation-generation/.github/workflows/attestation_generation.yml@0528f71469232b814108fe52f3418f8b8d10a0f8 #v1.0.1
    with:
      artifact_name: "artifact.zip"

  publish:
    runs-on: ubuntu-latest
    if: (github.event.inputs != '')
    needs: [ build ]
    steps:
      # Check out repo at latest commit
      - name: Checkout Git Repo
        uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7

      - name: Publish Dev/Hotfix Release
        shell: sh
        run: gh release edit ${{ needs.build.outputs.BRANCH_RELEASE_TAG }} --draft=false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
