name: TrOrg-CloudIac-ProjectPublish

on:
  workflow_run:
    workflows: [TrOrg-CloudIaC-ProjectCi]
    types:
      - completed
    branches:
      - main

env:
  # Define git user email and name to be used for git commits
  GIT_USER_EMAIL: "<EMAIL>"
  GIT_USER_NAME: "GitHub Actions"

jobs:
  release:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    steps:
      - uses: release-drafter/release-drafter@3f0f87098bd6b5c5b9a36d49c41d998ea58f9348 # v6.0.0
        with:
          publish: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get All Releases
        id: get-releases
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            async function getAllReleases() {
              let releases = await github.paginate(github.rest.repos.listReleases,{
                owner: context.repo.owner,
                repo: context.repo.repo,
                per_page: 100,
              },
              (response) => response.data.map((release) => ({
                id: release.id,
                tag_name: release.tag_name,
                created_at: release.created_at,
                name: release.name,
                target_commitish: release.target_commitish,
                draft: release.draft,
                prerelease: release.prerelease,
                published_at: release.published_at,
              })));

              return releases;
            }

            return getAllReleases();
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete Older Releases
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            async function deleteOlderReleases(releases, releasesToKeep) {
              if (releases.length <= releasesToKeep) {
                console.log("No older releases to delete.");
                return;
              }

              releases.sort((a, b) => new Date(b.published_at) - new Date(a.published_at));

              const releasesToDelete = releases.slice(releasesToKeep);

              for (const release of releasesToDelete) {
                await github.rest.repos.deleteRelease({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  release_id: release.id
                });
                console.log(`Deleted release ${release.tag_name}`);
              }
            }

            const releases = ${{ steps.get-releases.outputs.result }};
            const releasesToKeep = parseInt(process.env.RELEASES_TO_KEEP || 20);

            return deleteOlderReleases(releases, releasesToKeep);
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}