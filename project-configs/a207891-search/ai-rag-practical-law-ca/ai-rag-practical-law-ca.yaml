---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: ai-rag-practical-law-ca
github_repo: ras-search_ai-rag-practical-law-ca
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://ai-rag-practical-law-ca-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://ai-rag-practical-law-ca.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: ai-rag-practical-law-ca-ci
blue_green_helm_release_name_int: ai-rag-practical-law-ca-int
blue_green_helm_release_name_qa: ai-rag-practical-law-ca-qa
blue_green_helm_release_name_prod: ai-rag-practical-law-ca-prod

plexus_namespace: ras-search-ai

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ai-acceleration-test-suite-preprod-use1
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ai-acceleration-test-suite-preprod-use1
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ai-acceleration-test-suite-preprod-use1
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:935712178677:project/a207891-ai-acceleration-test-suite-prod-use1

ci_testspec_params:
  ENV: ci
  APPLICATION_DNS: https://ai-rag-practical-law-ca-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  PYTEST_MARK: postdeploy_rag_practicallaw_ca

int_testspec_params:
  ENV: int
  APPLICATION_DNS: https://ai-rag-practical-law-ca-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  PYTEST_MARK: postdeploy_rag_practicallaw_ca

qa_testspec_params:
  ENV: qa
  APPLICATION_DNS: https://ai-rag-practical-law-ca-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  PYTEST_MARK: postdeploy_rag_practicallaw_ca

prod_testspec_params:
  ENV: prod
  APPLICATION_DNS: https://ai-rag-practical-law-ca.plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  PYTEST_MARK: postdeploy_rag_practicallaw_ca

ci_automated_testing_enabled: 'True'
int_automated_testing_enabled: 'True'
qa_automated_testing_enabled: 'True'
prod_automated_testing_enabled: 'True'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_ci: ai-rag-practical-law-ca-ci-infra
simple_eks_helm_release_name_int: ai-rag-practical-law-ca-int-infra
simple_eks_helm_release_name_qa: ai-rag-practical-law-ca-qa-infra
simple_eks_helm_release_name_prod: ai-rag-practical-law-ca-prod-infra

ci_simple_eks_helm_set_values:
  environment: ci
  service_iam_role: arn:aws:iam::653661534649:role/a207891-ai-rag-practical-law-ca-ci-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  service_iam_role: arn:aws:iam::653661534649:role/a207891-ai-rag-practical-law-ca-int-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  service_iam_role: arn:aws:iam::653661534649:role/a207891-ai-rag-practical-law-ca-qa-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  service_iam_role: arn:aws:iam::935712178677:role/a207891-ai-rag-practical-law-ca-prod-use1-eks
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2