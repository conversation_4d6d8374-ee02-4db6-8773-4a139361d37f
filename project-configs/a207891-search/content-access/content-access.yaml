---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: content-access
github_repo: ras-search_content-access
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://content-access-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://content-access.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: content-access-ci
blue_green_helm_release_name_int: content-access-int
blue_green_helm_release_name_qa: content-access-qa
blue_green_helm_release_name_prod: content-access-prod

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:653661534649:project/a207891-search-content-access-automated-test
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:653661534649:project/a207891-search-content-access-automated-test
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:653661534649:project/a207891-search-content-access-automated-test
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:935712178677:project/a207891-search-content-access-automated-test

ci_testspec_params:
  ENVIRONMENT: ci
  APPLICATION_DNS: https://content-access-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

int_testspec_params:
  ENVIRONMENT: int
  APPLICATION_DNS: https://content-access-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

qa_testspec_params:
  ENVIRONMENT: qa
  APPLICATION_DNS: https://content-access-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

prod_testspec_params:
  ENVIRONMENT: prod
  APPLICATION_DNS: https://content-access-prod.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

ci_automated_testing_enabled: 'False'
int_automated_testing_enabled: 'False'
qa_automated_testing_enabled: 'True'
prod_automated_testing_enabled: 'True'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_ci: content-access-ci-infra
simple_eks_helm_release_name_int: content-access-int-infra
simple_eks_helm_release_name_qa: content-access-qa-infra
simple_eks_helm_release_name_prod: content-access-prod-infra

ci_simple_eks_helm_set_values:
  environment: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2