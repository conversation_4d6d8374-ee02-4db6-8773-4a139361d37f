---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/ecs/pipelinespec.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/ecs/bakespec-secpipe.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/ecs/blue-green-deployspec.yaml
  simple_cfn_deployspec: cumulus-templates-c5s-pipeline/deployspecs/ecs/simple-cfn-deployspec.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: search-engine
github_repo: ras-search_search-engine
github_repo_artifact_suffix: release
plexus_prod_account_id: '************'
plexus_preprod_account_id: '************'
prod_snow_approval_user: PowerUser2

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_ci: https://searchengine-ci.ras.int.thomsonreuters.com
application_dns_qa: https://searchengine-qa.ras.int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
int_alb_listener_arn: arn:aws:elasticloadbalancing:us-east-1:************:listener/app/a207891-search-engine-int-alb/8ef0fbcc061fca14/1e9ea44ad3976452
ci_alb_listener_arn: arn:aws:elasticloadbalancing:us-east-1:************:listener/app/a207891-search-engine-ci-alb/89146d6b02224a59/b19f9c1a6a6d5ba2
qa_alb_listener_arn: arn:aws:elasticloadbalancing:us-east-1:************:listener/app/a207891-search-engine-qa-alb/6fb8c27ad6bdcb3b/0a3f1ee5ce8e31d5
prod_alb_listener_arn: arn:aws:elasticloadbalancing:us-east-1:************:listener/app/a207891-search-engine-prod-alb/ca36464b6ea5fd37/e0c55835448d4b38

security_groups_int: 'sg-0cca55be7aa1763c7,sg-038435d01cd3063aa,sg-0fa79fafbd69e12da,sg-023c1da536cadd3c8,sg-075d0f2d339fac134'
security_groups_ci: 'sg-0cca55be7aa1763c7,sg-04edc26f8e9ee4232,sg-0ae8e3c184cbbd938,sg-038435d01cd3063aa,sg-070f36b25a1ca2894'
security_groups_qa: 'sg-0cca55be7aa1763c7,sg-04edc26f8e9ee4232,sg-0ae8e3c184cbbd938,sg-038435d01cd3063aa,sg-0648bb66a66854c72'
security_groups_prod: 'sg-07d243036b490c36b,sg-0cbb16330589dd513,sg-0231fde778428d471,sg-070ea1b39f4b31488,sg-0d50cbfc49447552a'

generic_failure_minutes: '5'
routing_wait_time_seconds: '12'
routing_shift_percentage: '50'
routing_group_stickiness_duration_seconds: '10'
routing_target_rule_priority: '20000'
routing_green_rule_priority: '11999'
routing_unhealthy_threshold_count: '4'

container_health_check_grace_period_seconds: '1800'

log_expiry_days: '14'

read_only_root_filesystem: 'true'

int_ci_task_memory: '7049'
qa_task_memory: '56389'
prod_task_memory: '117856'

int_env_name: int
ci_env_name: ci
qa_env_name: qa
prod_env_name: prod

container_desired_count: '1'

ecs_service_log_group_int: a207891-search-engine-live-int-ecs-logs
ecs_service_log_group_ci: a207891-search-engine-live-ci-ecs-logs
ecs_service_log_group_qa: a207891-search-engine-live-qa-ecs-logs
ecs_service_log_group_prod: a207891-search-engine-live-prod-ecs-logs

efs_source_volume_int: a207891-search-engine-live-int-efs2-use1
efs_source_volume_qa: a207891-search-engine-live-qa-efs2-use1
efs_source_volume_prod: a207891-search-engine-live-prod-efs2-use1

efs_access_point_id_int: fsap-04d957dd85a99a5a1
efs_access_point_id_qa: fsap-04c3dd8a70c8d1d5f
efs_access_point_id_prod: fsap-02f72458003a8ddba

efs_file_system_id_int: fs-05625d5a0dd4f5b04
efs_file_system_id_qa: fs-0c7986538384eb1cd
efs_file_system_id_prod: fs-03d223f543d84079c

iam_instance_role_int: a207891-search-engine-live-int-inst-role-use1
iam_instance_role_ci: a207891-search-engine-live-ci-inst-role-use1
iam_instance_role_qa: a207891-search-engine-live-qa-inst-role-use1
iam_instance_role_prod: a207891-search-engine-live-prod-inst-role-use1

key_pair_name_ci: a207891-search-engine-ci
key_pair_name_qa: a207891-search-engine-qa

datadog_account_preprod: tr-contentandresearch-preprod
datadog_account_prod: tr-fed-contentandresearch-prod

testing_sns_arn: arn:aws:sns:us-east-1:************:a207891-ras-search-application-approval
testing_codebuild_arn_preprod: arn:aws:codebuild:us-east-1:************:project/a207891-ras-search-shared-test-suite-preprod-use1
testing_codebuild_arn_prod: arn:aws:codebuild:us-east-1:************:project/a207891-ras-search-shared-test-suite-prod-use1
testing_application_dns_int: https://searchengine-int.ras.int.thomsonreuters.com
testing_application_dns_ci: https://searchengine-ci.ras.int.thomsonreuters.com
testing_application_dns_qa: https://searchengine-qa.ras.int.thomsonreuters.com
testing_application_dns_prod: https://searchengine.ras.int.thomsonreuters.com

################################################################
# variables required within the simple-cfn deployspec template #
################################################################
container_role_int: arn:aws:iam::************:role/a207891-search-engine-live-int-ecs-use1
container_role_ci: arn:aws:iam::************:role/a207891-search-engine-live-ci-ecs-use1
container_role_qa: arn:aws:iam::************:role/a207891-search-engine-live-qa-ecs-use1
container_role_prod: arn:aws:iam::************:role/a207891-search-engine-live-prod-ecs-use1

cluster_name_int: a207891-search-engine-live-int-ecs-cluster
cluster_name_ci: a207891-search-engine-live-ci-ecs-cluster
cluster_name_qa: a207891-search-engine-live-qa-ecs-cluster
cluster_name_prod: a207891-search-engine-live-prod-ecs-cluster

datadog_api_key_preprod: arn:aws:secretsmanager:us-east-1:************:secret:a207891/ras-search/datadog/api-secret-ozSj5w
datadog_api_key_prod: arn:aws:secretsmanager:us-east-1:************:secret:a207891/ras-search/datadog/api-secret-wawu6v
datadog_site_preprod: datadoghq.com
datadog_site_prod: ddog-gov.com
datadog_fips_disabled: '0'
datadog_fips_enabled: '1'
