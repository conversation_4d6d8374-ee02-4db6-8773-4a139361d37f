---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/cloud-iac/pipelinespec-single-env-v2.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/cloud-iac/bakespec-do-nothing-special-v2.yaml
  cloud_iac_deployspec: cumulus-templates-c5s-pipeline/deployspecs/cloud-iac/cloud-iac-deployspec-single-env-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/cloud-iac/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/cloud-iac/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: search-shared-iac
github_repo: ras-search_shared-iac
github_repo_artifact_suffix: release

###############################################################
# variables required within the cloud-iac deployspec template #
###############################################################
iac_stage_one_name: cicd # you can call this whatever you want, it is just a name to identify the first stage of the cloud-iac deployspec and pipelinespec files
iac_stage_one_env_override: cicd # this needs to match the folder name in your iac repo that you wish to execute/ deploy
iac_stage_one_account_id: "************" # this needs to match the account id of where you want your resources to be deployed
