---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-dev-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-hack-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-dev-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-dev-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: ai-rag-westlaw-claims-explorer-dev
special_micro_service_name: ai-rag-westlaw-claims-explorer # this is the name of the microservice without the -dev suffix, this fixes the ECR naming issue Dong and I ran into. you need to use bakespec-python-version-option-dev-v1.yaml in order for this fix to work
github_repo: ras-search_ai-rag-westlaw-claims-explorer
github_repo_artifact_suffix: dev

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://ai-rag-westlaw-claims-explorer-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://ai-rag-westlaw-claims-explorer.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_dev: ai-rag-westlaw-claims-explorer-dev

plexus_namespace: ras-search-ai

dev_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: dev
  springProfile: dev
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_dev: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ai-acceleration-test-suite-preprod-use1

dev_testspec_params:
  ENV: dev
  APPLICATION_DNS: https://ai-conversations-dev.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  PYTEST_MARK: "postdeploy_rag_westlaw_claims_explorer"

dev_automated_testing_enabled: 'True'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
simple_eks_helm_release_name_dev: ai-rag-westlaw-claims-explorer-dev-infra

dev_simple_eks_helm_set_values:
  environment: dev
  service_iam_role: arn:aws:iam::653661534649:role/a207891-ai-rag-westlaw-claims-explorer-ci-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2