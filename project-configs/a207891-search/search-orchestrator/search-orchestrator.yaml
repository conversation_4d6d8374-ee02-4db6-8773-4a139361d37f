---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-secpipe-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: search-orch
github_repo: ras-search_search-orchestrator
github_repo_artifact_suffix: release

simple_eks_helm_root: "helm/infrastructure"
blue_green_helm_root: "helm/application"
bakespec_helm_folder_base: "helm"

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://searchorchestrator-qa.ras.int.thomsonreuters.com
application_dns_prod: https://searchorchestrator.ras.int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_dev: searchorchestrator-dev
blue_green_helm_release_name_ci: searchorchestrator-ci
blue_green_helm_release_name_int: searchorchestrator-int
blue_green_helm_release_name_qa: searchorchestrator-qa
blue_green_helm_release_name_prod: searchorchestrator-prod

plexus_namespace: search

dev_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: dev
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_dev: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ras-search-shared-test-suite-preprod-use1
test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ras-search-shared-test-suite-preprod-use1
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ras-search-shared-test-suite-preprod-use1
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:653661534649:project/a207891-ras-search-shared-test-suite-preprod-use1
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:935712178677:project/a207891-ras-search-shared-test-suite-prod-use1

dev_testspec_params:
  APPLICATION_DNS: https://searchorchestrator-dev.ras.int.thomsonreuters.com
  TEST_ENVIRONMENT: DEV

ci_testspec_params:
  APPLICATION_DNS: https://searchorchestrator-ci.ras.int.thomsonreuters.com
  TEST_ENVIRONMENT: CI

int_testspec_params:
  APPLICATION_DNS: https://searchorchestrator-int.ras.int.thomsonreuters.com
  TEST_ENVIRONMENT: INT

qa_testspec_params:
  APPLICATION_DNS: https://searchorchestrator-qa.ras.int.thomsonreuters.com
  TEST_ENVIRONMENT: QA

prod_testspec_params:
  APPLICATION_DNS: https://searchorchestrator.ras.int.thomsonreuters.com
  TEST_ENVIRONMENT: PROD

dev_automated_testing_enabled: 'True'
ci_automated_testing_enabled: 'True'
int_automated_testing_enabled: 'False'
qa_automated_testing_enabled: 'True'
prod_automated_testing_enabled: 'True'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_dev: searchorch-dev-infra
simple_eks_helm_release_name_ci: searchorch-ci-infra
simple_eks_helm_release_name_int: searchorch-int-infra
simple_eks_helm_release_name_qa: searchorch-qa-infra
simple_eks_helm_release_name_prod: searchorch-prod-infra

dev_simple_eks_helm_set_values:
  environment: dev
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

ci_simple_eks_helm_set_values:
  environment: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2
