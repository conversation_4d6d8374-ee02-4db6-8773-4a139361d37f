---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/ec2/pipelinespec.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/ec2/bakespec.yaml
  simple_cfn_deployspec: cumulus-templates-c5s-pipeline/deployspecs/ec2/simple-cfn-deployspec.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml


####################################################
# project specific variables found below this line #
####################################################
micro_service_name: eureka
github_repo: ras-search_eureka-cmls
github_repo_artifact_suffix: release
prod_snow_approval_user: PowerUser2

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_ci: https://eureka-ci.ras.int.thomsonreuters.com
application_dns_qa: https://eureka-qa.ras.int.thomsonreuters.com

################################################################
# variables required within the simple-cfn-deployspec template #
################################################################

# Common parameters
bootstrap_script: cicd/deploy/bootstrap/bootstrap.sh
target_group_stickiness_enabled: 'true'
resource_owner_preprod: '<EMAIL>'
resource_owner_prod: '<EMAIL>'
subnets_preprod: 'subnet-05b61b5709578a94b, subnet-03d2545d25b4581b9, subnet-0913d4a150a37def1'
subnets_prod: 'subnet-0344b90bbd37ff2d7, subnet-02be6ed821a02be09, subnet-0d1f81f714bc34774'

# Environment-specific security groups
instance_security_groups_dev: sg-0b40fc195bc96cd7b
instance_security_groups_ci: sg-0dce74d6d04767c39
instance_security_groups_int: sg-0770f30ff23118381
instance_security_groups_qa: sg-0db7c2e4bc77580bc
instance_security_groups_prod: sg-0cd025ca3473a15e2

# Environment-specific listener ARNs
listener_arn_dev: 'arn:aws:elasticloadbalancing:us-east-1:653661534649:listener/app/a207891-eureka-dev-alb/f2df3558c4f84a91/8a5d80b4284f5213'
listener_arn_ci: 'arn:aws:elasticloadbalancing:us-east-1:653661534649:listener/app/a207891-eureka-ci-alb/baf940ec0716ccbb/05c2635d1013816e'
listener_arn_int: 'arn:aws:elasticloadbalancing:us-east-1:653661534649:listener/app/a207891-eureka-int-alb/cd6eea7659f6b04a/189fb5c6a8232cf4'
listener_arn_qa: 'arn:aws:elasticloadbalancing:us-east-1:653661534649:listener/app/a207891-eureka-qa-alb/8d41026e5495a8d2/71fae259abe6cb24'
listener_arn_prod: 'arn:aws:elasticloadbalancing:us-east-1:935712178677:listener/app/a207891-eureka-prod-alb/d68e261a06c3e466/64e2a3eaba84e292'

# Environment-specific IAM instance profiles
iam_instance_profile_dev: 'arn:aws:iam::653661534649:instance-profile/a207891-eureka-instance-profile-dev'
iam_instance_profile_ci: 'arn:aws:iam::653661534649:instance-profile/a207891-eureka-instance-profile-ci'
iam_instance_profile_int: 'arn:aws:iam::653661534649:instance-profile/a207891-eureka-instance-profile-int'
iam_instance_profile_qa: 'arn:aws:iam::653661534649:instance-profile/a207891-eureka-instance-profile-qa'
iam_instance_profile_prod: 'arn:aws:iam::935712178677:instance-profile/a207891-eureka-instance-profile-prod'

# Environment-specific Datadog FIPS mode configuration
datadog_fips_enabled_dev: 'false'
datadog_fips_enabled_ci: 'false'
datadog_fips_enabled_int: 'false'
datadog_fips_enabled_qa: 'false'
datadog_fips_enabled_prod: 'true'

# Datadog configuration
datadog_api_key_preprod: 'arn:aws:secretsmanager:us-east-1:653661534649:secret:a207891/ras-search/datadog/api-secret-ozSj5w'
datadog_api_key_prod: 'arn:aws:secretsmanager:us-east-1:935712178677:secret:a207891/ras-search/datadog/api-secret-wawu6v'
datadog_site_preprod: datadoghq.com
datadog_site_prod: ddog-gov.com
