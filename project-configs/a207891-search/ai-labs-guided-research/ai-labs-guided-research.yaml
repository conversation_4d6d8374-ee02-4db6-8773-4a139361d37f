---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  labs_pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-labs-v3.yaml
  labs_bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-branch-prototype-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-labs-v2.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-labs-v2.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: ai-labs-guided-research
github_repo: ras-search_ai-labs-guided-research
github_repo_artifact_suffix_labs: branch-prototype

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://ai-labs-guided-research-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://ai-labs-guided-research.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: ai-labs-guided-research-REPLACE_BRANCH_NAME-ci
blue_green_helm_release_name_int: ai-labs-guided-research-REPLACE_BRANCH_NAME-int
blue_green_helm_release_name_qa: ai-labs-guided-research-REPLACE_BRANCH_NAME-qa
blue_green_helm_release_name_prod: ai-labs-guided-research-REPLACE_BRANCH_NAME-prod
blue_green_helm_release_name_labs: ai-labs-guided-research-REPLACE_BRANCH_NAME-labs

plexus_namespace: ras-search-ai

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

labs_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: labs
  springProfile: labs
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:************:project/a207891-ai-acceleration-test-suite-preprod-use1
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:************:project/a207891-ai-acceleration-test-suite-preprod-use1
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:************:project/a207891-ai-acceleration-test-suite-preprod-use1
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:935712178677:project/a207891-ai-acceleration-test-suite-prod-use1
test_suite_codebuild_arn_labs: arn:aws:codebuild:us-east-1:************:project/a207891-ai-acceleration-test-suite-preprod-use1

ci_testspec_params:
  ENV: ci
  APPLICATION_DNS: https://ai-conversations-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

int_testspec_params:
  ENV: int
  APPLICATION_DNS: https://ai-conversations-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

qa_testspec_params:
  ENV: qa
  APPLICATION_DNS: https://ai-conversations-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

prod_testspec_params:
  ENV: prod
  APPLICATION_DNS: https://ai-conversations-prod.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

labs_testspec_params:
  ENV: labs
  APPLICATION_DNS: https://ai-conversations-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

ci_automated_testing_enabled: 'False'
int_automated_testing_enabled: 'False'
qa_automated_testing_enabled: 'False'
prod_automated_testing_enabled: 'False'
labs_automated_testing_enabled: 'False'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_ci: ai-labs-guided-research-REPLACE_BRANCH_NAME-ci-infra
simple_eks_helm_release_name_int: ai-labs-guided-research-REPLACE_BRANCH_NAME-int-infra
simple_eks_helm_release_name_qa: ai-labs-guided-research-REPLACE_BRANCH_NAME-qa-infra
simple_eks_helm_release_name_prod: ai-labs-guided-research-REPLACE_BRANCH_NAME-prod-infra
simple_eks_helm_release_name_labs: ai-labs-guided-research-REPLACE_BRANCH_NAME-labs-infra

ci_simple_eks_helm_set_values:
  environment: ci
  service_iam_role: arn:aws:iam::************:role/a207891-ai-rag-skills-prototype-ci-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  service_iam_role: arn:aws:iam::************:role/a207891-ai-rag-skills-prototype-int-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  service_iam_role: arn:aws:iam::************:role/a207891-ai-rag-skills-prototype-qa-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  service_iam_role: arn:aws:iam::935712178677:role/a207891-ai-rag-skills-prototype-prod-use1-eks
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

labs_simple_eks_helm_set_values:
  environment: labs
  service_iam_role: arn:aws:iam::************:role/a207891-ai-rag-skills-prototype-labs-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2