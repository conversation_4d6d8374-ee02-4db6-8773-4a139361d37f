# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/cloud-iac/pipelinespec-six-env-v2.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/cloud-iac/bakespec-do-nothing-special-v2.yaml
  cloud_iac_deployspec: cumulus-templates-c5s-pipeline/deployspecs/cloud-iac/cloud-iac-deployspec-v2.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/cloud-iac/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/cloud-iac/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: search-engine-iac
github_repo: ras-search_search-engine-iac
github_repo_artifact_suffix: release

###############################################################
# variables required within the cloud-iac deployspec template #
###############################################################
iac_env_override_cicd: prod-cicd # we need to override since the repo does not just call it "cicd" but instead "prod-cicd"