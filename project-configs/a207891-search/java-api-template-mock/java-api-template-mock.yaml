---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207891-search/shared-variables-sandbox-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-mock-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-mock-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-mock-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-mock-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-mock-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-mock-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: java-api-template-mock
github_repo: ras-search_java-api-template-mock
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://java-api-template-mock-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_qa: java-api-template-mock-qa

plexus_namespace: ras-search-ai

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:653661534649:project/a207891-java-api-template-mock-test-suite-preprod-use1
qa_testspec_params:
  ENV: qa

qa_automated_testing_enabled: 'False'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_qa: java-api-template-mock-qa-infra

qa_simple_eks_helm_set_values:
  environment: qa
  service_iam_role: arn:aws:iam::653661534649:role/a207891-java-api-template-mock-qa-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2