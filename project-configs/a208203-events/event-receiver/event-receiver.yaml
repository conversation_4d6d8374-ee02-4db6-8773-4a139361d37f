---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a208203-events/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: event-receiver
github_repo: ras-event_event-receiver
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://event-receiver-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://event-receiver-prod.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: event-receiver-ci
blue_green_helm_release_name_int: event-receiver-int
blue_green_helm_release_name_qa: event-receiver-qa
blue_green_helm_release_name_prod: event-receiver-prod

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2
  service_iam_role: arn:aws:iam::718074479902:role/a208203-ras-event-event-receiver-eks-assume-prod-use1
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:849639756666:project/a208203-ras-event-event-receiver-testsuite-ci-use1
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:849639756666:project/a208203-ras-event-event-receiver-testsuite-int-use1
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:849639756666:project/a208203-ras-event-event-receiver-testsuite-qa-use1
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:718074479902:project/a208203-ras-event-event-receiver-testsuite-prod-use1

ci_testspec_params:
  ENV: CI
  APPLICATION_DNS: https://event-receiver-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

int_testspec_params:
  ENV: INT
  APPLICATION_DNS: https://event-receiver-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

qa_testspec_params:
  ENV: QA
  APPLICATION_DNS: https://event-receiver-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

prod_testspec_params:
  ENV: PROD
  APPLICATION_DNS: https://event-receiver.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

ci_automated_testing_enabled: 'False'
int_automated_testing_enabled: 'False'
qa_automated_testing_enabled: 'False'
prod_automated_testing_enabled: 'False'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
simple_eks_helm_release_name_ci: event-receiver-ci-infra
simple_eks_helm_release_name_int: event-receiver-int-infra
simple_eks_helm_release_name_qa: event-receiver-qa-infra
simple_eks_helm_release_name_prod: event-receiver-prod-infra

ci_simple_eks_helm_set_values:
  environment: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2
  service_iam_role: arn:aws:iam::849639756666:role/a208203-ras-event-event-receiver-eks-assume-ci-use1
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

int_simple_eks_helm_set_values:
  environment: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2
  service_iam_role: arn:aws:iam::849639756666:role/a208203-ras-event-event-receiver-eks-assume-int-use1
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

qa_simple_eks_helm_set_values:
  environment: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2
  service_iam_role: arn:aws:iam::849639756666:role/a208203-ras-event-event-receiver-eks-assume-qa-use1
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com

prod_simple_eks_helm_set_values:
  environment: prod
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2
  service_iam_role: arn:aws:iam::718074479902:role/a208203-ras-event-event-receiver-eks-assume-prod-use1
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com