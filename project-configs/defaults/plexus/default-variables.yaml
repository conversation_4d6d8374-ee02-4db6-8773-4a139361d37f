preprod_snow_approval_user: PowerUser2
prod_snow_approval_user: PlexusNamespaceOps
snow_approval_user: PlexusNamespaceOps

optional_ecr_postfix: ''
dockerfile_location: './Dockerfile'

region_long: us-east-1
region_short: use1

dev_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: dev

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int

qa_blue_green_auto_approve: 'False'

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod

labs_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: labs

dev_simple_eks_helm_set_values:
  environment: dev

ci_simple_eks_helm_set_values:
  environment: ci

int_simple_eks_helm_set_values:
  environment: int

qa_simple_eks_helm_set_values:
  environment: qa

prod_simple_eks_helm_set_values:
  environment: prod

labs_simple_eks_helm_set_values:
  environment: labs

# For projects that don't have a dev pipeline these values were flagging as missing so including them in defaults
blue_green_helm_release_name_dev: 'default'
simple_eks_helm_release_name_dev: 'default'
test_suite_codebuild_arn_dev: 'default'
dev_testspec_params:
  ENV: DEV

# For projects that don't have a labs pipeline these values were flagging as missing so including them in defaults
blue_green_helm_release_name_labs: 'default'
simple_eks_helm_release_name_labs: 'default'
test_suite_codebuild_arn_labs: 'default'
labs_testspec_params:
  ENV: LABS

# Default Test Spec locations
dev_testspec_filepath: cicd/cumulus-testspec.yaml
ci_testspec_filepath: cicd/cumulus-testspec.yaml
int_testspec_filepath: cicd/cumulus-testspec.yaml
qa_testspec_filepath: cicd/cumulus-testspec.yaml
prod_testspec_filepath: cicd/cumulus-testspec.yaml
labs_testspec_filepath: cicd/cumulus-testspec.yaml

# TODO: old flag for enabling testing on and off for all lower envs
automated_testing_enabled: 'True'
# TODO: the above should be phased out of the templates but I will leave it here so it does not break people's pipelines


# Flags to turn on or off testing for given env
dev_automated_testing_enabled: 'True'
ci_automated_testing_enabled: 'True'
int_automated_testing_enabled: 'True'
qa_automated_testing_enabled: 'True'
prod_automated_testing_enabled: 'True'
labs_automated_testing_enabled: 'True'

# NOTE: most projects don't have a dev plexus env, so we default to the ci namespace for dev pipelines
deployspec_plexus_namespace_dev_env_postfix: ci
