---
# default values that can be used or overwritten if you choose to in your shared or project specific variables files
preprod_snow_approval_user: PowerUser2
prod_snow_approval_user: PlexusNamespaceOps

# default values used within the blue-green deployspec templates
dev_automated_testing_enabled: 'False'
ci_automated_testing_enabled: 'True'
int_automated_testing_enabled: 'True'
qa_automated_testing_enabled: 'True'
prod_automated_testing_enabled: 'True'
labs_automated_testing_enabled: 'True'

dev_testspec_filepath: cicd/cumulus-testspec.yaml
ci_testspec_filepath: cicd/cumulus-testspec.yaml
int_testspec_filepath: cicd/cumulus-testspec.yaml
qa_testspec_filepath: cicd/cumulus-testspec.yaml
prod_testspec_filepath: cicd/cumulus-testspec.yaml
labs_testspec_filepath: cicd/cumulus-testspec.yaml

blue_green_wait_minutes: '13'
simple_eks_wait_minutes: '13'

dockerfile_location: './Dockerfile'

qa_blue_green_auto_approve: 'False'

dev_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: dev

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod

labs_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod

# default values used within the simple eks deployspec templates
deployspec_plexus_namespace_dev_env_postfix: ci # NOTE: most projects don't have a dev plexus env, so we default to the ci namespace for dev pipelines

dev_simple_eks_helm_set_values:
  environment: ci

ci_simple_eks_helm_set_values:
  environment: ci

int_simple_eks_helm_set_values:
  environment: int

qa_simple_eks_helm_set_values:
  environment: qa

prod_simple_eks_helm_set_values:
  environment: prod

labs_simple_eks_helm_set_values:
  environment: prod

###############################################################
# variables required within the cloud-iac deployspec template #
###############################################################
iac_env_override_cicd: cicd
iac_env_override_dev: dev
iac_env_override_ci: ci
iac_env_override_int: int
iac_env_override_qa: qa
iac_env_override_prod: prod
