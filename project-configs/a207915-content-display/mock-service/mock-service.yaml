---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207915-content-display/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-ci-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-ci-v2.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-ci-v2.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: mock-service
github_repo: ras_content-display-mock-service
github_repo_artifact_suffix: release

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: mock-service

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:833874627088:project/a207915-mock-service-NO-CODEBUILD-FOR-THIS-PROJECT

ci_testspec_params:
  APPLICATION_DNS: https://cd-mock-service-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: CI

ci_automated_testing_enabled: 'False'

################################################################
# variables required within the simple-eks deployspec template #
################################################################
simple_eks_helm_release_name_ci: mock-service-infra

ci_simple_eks_helm_set_values:
  environment: ci
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::229708435771:role/a207915-ras-content-display-mock-service-ci-ue1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2