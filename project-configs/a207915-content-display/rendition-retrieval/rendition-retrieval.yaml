---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207915-content-display/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: rendition-retrieval
github_repo: ras_content-display-rendition-retrieval
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://rendition-retrieval-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://rendition-retrieval.ras.int.thomsonreuters.com # TODO: someone familiar with this project needs to vet this value for ras2

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: rendition-retrieval
blue_green_helm_release_name_int: rendition-retrieval
blue_green_helm_release_name_qa: rendition-retrieval
blue_green_helm_release_name_prod: rendition-retrieval

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:833874627088:project/a207915-rendition-retrieval-ci-automated-test
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:833874627088:project/a207915-rendition-retrieval-int-automated-test
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:833874627088:project/a207915-rendition-retrieval-qa-automated-test
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:039118391688:project/a207915-rendition-retrieval-prod-automated-test

ci_testspec_params:
  APPLICATION_DNS: https://rendition-retrieval-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: CI

int_testspec_params:
  APPLICATION_DNS: https://rendition-retrieval-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: INT

qa_testspec_params:
  APPLICATION_DNS: https://rendition-retrieval-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: QA

prod_testspec_params:
  APPLICATION_DNS: https://rendition-retrieval.ras.int.thomsonreuters.com # TODO: someone familiar with this project needs to vet this value for ras2
  ENV: PROD

ci_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
int_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
qa_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
prod_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over

################################################################
# variables required within the simple-eks deployspec template #
################################################################
simple_eks_helm_release_name_ci: rendition-retrieval-infra
simple_eks_helm_release_name_int: rendition-retrieval-infra
simple_eks_helm_release_name_qa: rendition-retrieval-infra
simple_eks_helm_release_name_prod: rendition-retrieval-infra

ci_simple_eks_helm_set_values:
  environment: ci
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::833874627088:role/a207915-ras-cd-rendition-retrieval-ci-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::833874627088:role/a207915-ras-cd-rendition-retrieval-int-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::833874627088:role/a207915-ras-cd-rendition-retrieval-qa-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::039118391688:role/a207915-ras-cd-rendition-retrieval-prod-use1-eks
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2