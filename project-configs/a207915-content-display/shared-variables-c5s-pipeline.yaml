---
#################################################################################
# cumulus installspec template location found within the shared templates' repo #
#################################################################################
installspec_file: cumulus-templates-c5s-pipeline/installspecs/installspec-v1.yaml

# note: I had to run the following commands from my local machine to get past the issue of qa env not deploying due to cloud formation stacks being too large
# more info can be found here to set this up on your local machine https://github.com/tr/nuvola_tr-cdk-lib/blob/main/packages/core/docs/TR-bootstrapper.md
# trcdk bootstrap aws://************/us-east-1 --profile=default --asset-id=207915 --resource-owner=<EMAIL> --environment-type=PRODUCTION
# trcdk bootstrap aws://************/us-east-2 --profile=default --asset-id=207915 --resource-owner=<EMAIL> --environment-type=PRODUCTION
# in order to do the above I needed to cloud tool into the cicd account under power user 2 & be in the correct region before running the command

##################################################
# variables used within the installspec template #
##################################################
cumulus_version: 1.28.1
cloud_iac_version: 3.2.25
asset_id: '207915'
group_name: ras-con-prod
cicd_account_id: '************'
environment_type: PRODUCTION
resource_owner: <EMAIL>

###############################################
# variables used within the bakespec template #
###############################################
service_name: ras-content-display
artifactory_token_secret_name: a207915/ras/content-display/artifactory/secure-token
bakespec_helm_folder_base: "helm2"

####################################################
# variables used within the pipelinespec templates #
####################################################
cumulus_namespace: ras-content-display
# TODO: idk how we are going to handle multiple regions yet ...
# TODO: this will have to be revisited when cumulus figures out how multi region works with single pipelinespec file
dev_notification_sns_arn: arn:aws:sns:us-east-1:************:a207915-ras-content-display-application-deployer
dev_approval_sns_arn: arn:aws:sns:us-east-1:************:a207915-ras-content-display-application-approval
ops_deployment_approval_sns_arn: arn:aws:sns:us-east-1:************:a207915-ras-content-display-prod-use1-application-approver
ops_release_approval_sns_arn: arn:aws:sns:us-east-1:************:a207915-ras-content-display-prod-use1-application-deployer

##################################################
# variables used within the deployspec templates #
##################################################
plexus_asset_id: '207915'
deployer_role: "human-role/a207915-PowerUser2"
prod_deployer_role: "human-role/a207915-PlexusNamespaceOps"
plexus_preprod_cluster_name: a207970-preprod-ras2-use1-plexus-cluster
plexus_prod_cluster_name: a207970-prod-ras2-use1-plexus-cluster
plexus_preprod_account_id: '************' # this is the service mesh pre prod account
plexus_prod_account_id: '************' # this is the service mesh prod account
simple_eks_helm_root: "helm2/infrastructure"
blue_green_helm_root: "helm2/application"
infra_preprod_account_id: "************" # this is the preprod account for the projects infrastructure
infra_prod_account_id: "************" # this is the prod account for the projects infrastructure

############################################################
# variables used within the blue-green deployspec template #
############################################################
plexus_namespace: content-display

##############################
## Region One // us-east-1  ##
##############################
region_one_long: us-east-1
region_one_short: use1
region_one_cicd_s3_kms_key_arn: arn:aws:kms:us-east-1:************:alias/a207915-ras-content-display-s3-prod-use1

##############################
## Region Two // us-east-2  ##
##############################
region_two_long: us-east-2
region_two_short: use2