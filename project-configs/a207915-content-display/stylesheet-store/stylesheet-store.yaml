---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a207915-content-display/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: stylesheet-store
github_repo: ras_content-display-stylesheet-store
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://stylesheet-store-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://stylesheet-store.plexus-ras2-use1.3772.aws-int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: stylesheet-store
blue_green_helm_release_name_int: stylesheet-store
blue_green_helm_release_name_qa: stylesheet-store
blue_green_helm_release_name_prod: stylesheet-store

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:833874627088:project/a207915-stylesheet-store-ci-automated-test
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:833874627088:project/a207915-stylesheet-store-int-automated-test
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:833874627088:project/a207915-stylesheet-store-qa-automated-test
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:039118391688:project/a207915-stylesheet-store-prod-automated-test

ci_testspec_params:
  APPLICATION_DNS: https://stylesheet-store-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: CI

int_testspec_params:
  APPLICATION_DNS: https://stylesheet-store-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: INT

qa_testspec_params:
  APPLICATION_DNS: https://stylesheet-store-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: QA

prod_testspec_params:
  APPLICATION_DNS: https://stylesheet-store.plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  ENV: PROD

ci_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
int_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
qa_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
prod_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_ci: stylesheet-store-infra
simple_eks_helm_release_name_int: stylesheet-store-infra
simple_eks_helm_release_name_qa: stylesheet-store-infra
simple_eks_helm_release_name_prod: stylesheet-store-infra

ci_simple_eks_helm_set_values:
  environment: ci
  service_iam_role: arn:aws:iam::833874627088:role/a207915-ras-cd-stylesheet-store-ci-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  service_iam_role: arn:aws:iam::833874627088:role/a207915-ras-cd-stylesheet-store-int-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  service_iam_role: arn:aws:iam::833874627088:role/a207915-ras-cd-stylesheet-store-qa-use1-eks
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  service_iam_role: arn:aws:iam::039118391688:role/a207915-ras-cd-stylesheet-store-prod-use1-eks
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2