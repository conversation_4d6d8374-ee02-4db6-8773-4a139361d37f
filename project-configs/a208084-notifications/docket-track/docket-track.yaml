---
#################################################################################################
# locations of shared variables and default variables found within the pipeline parameters repo #
#################################################################################################
shared_variables_file: project-configs/a208084-notifications/shared-variables-c5s-pipeline.yaml
defaults_variables_file: project-configs/defaults/default-variables-c5s-pipeline.yaml

################################################################################################
# new approach attempt to allow for any number of template files to be mentioned and generated #
################################################################################################
generate_following_cumulus_files_from_template_files:
  pipelinespec: cumulus-templates-c5s-pipeline/pipelinespecs/plexus/pipelinespec-v3.yaml
  bakespec: cumulus-templates-c5s-pipeline/bakespecs/plexus/bakespec-python-version-option-v2.yaml
  blue_green_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/blue-green-deployspec-v1.yaml
  simple_eks_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/simple-eks-deployspec-v1.yaml
  script_only_deployspec: cumulus-templates-c5s-pipeline/deployspecs/plexus/script-only-deployspec-v1.yaml
  ticket_close_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-close-v1.yaml
  ticket_open_snowspec: cumulus-templates-c5s-pipeline/snowspecs/plexus/snowticket-input-open-v1.yaml

####################################################
# project specific variables found below this line #
####################################################
micro_service_name: docket-track
github_repo: ras-notifications_docket-track
github_repo_artifact_suffix: release

#######################################################
# variables required within the pipelinespec template #
#######################################################
application_dns_qa: https://docket-track-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
application_dns_prod: https://docket-track.ras.int.thomsonreuters.com

################################################################
# variables required within the blue-green deployspec template #
################################################################
blue_green_helm_release_name_ci: docket-track # TODO: is this really correct? no envs in the names?
blue_green_helm_release_name_int: docket-track # TODO: is this really correct? no envs in the names?
blue_green_helm_release_name_qa: docket-track # TODO: is this really correct? no envs in the names?
blue_green_helm_release_name_prod: docket-track # TODO: is this really correct? no envs in the names?

ci_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: ci
  springProfile: ci
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: int
  springProfile: int
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: qa
  springProfile: qa
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_blue_green_helm_set_values:
  image_url: Value  # This value is filled in by the bake_helper.py script during the bake stage
  environment: prod
  springProfile: prod
  regionDD: us-east-1
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

test_suite_codebuild_arn_ci: arn:aws:codebuild:us-east-1:911277578290:project/a208084-docket-track-ci-use1-automated-test
test_suite_codebuild_arn_int: arn:aws:codebuild:us-east-1:911277578290:project/a208084-docket-track-int-use1-automated-test
test_suite_codebuild_arn_qa: arn:aws:codebuild:us-east-1:911277578290:project/a208084-docket-track-qa-use1-automated-test
test_suite_codebuild_arn_prod: arn:aws:codebuild:us-east-1:196165776688:project/a208084-docket-track-prod-use1-automated-test

ci_testspec_params:
  APPLICATION_DNS: https://docket-track-ci.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: CI

int_testspec_params:
  APPLICATION_DNS: https://docket-track-int.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: INT

qa_testspec_params:
  APPLICATION_DNS: https://docket-track-qa.plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  ENV: QA

prod_testspec_params:
  APPLICATION_DNS: https://docket-track.ras.int.thomsonreuters.com
  ENV: PROD

ci_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
int_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
qa_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over
prod_automated_testing_enabled: 'False' # TODO: turning testing off since kms issue still exists for this group, we need to fix this after the ras2 cut over

################################################################
# variables required within the simple-eks deployspec template #
################################################################
deployspec_plexus_namespace_dev_env_postfix: dev

simple_eks_helm_release_name_ci: docket-track-infra # TODO: is this really correct? no envs in the names?
simple_eks_helm_release_name_int: docket-track-infra # TODO: is this really correct? no envs in the names?
simple_eks_helm_release_name_qa: docket-track-infra # TODO: is this really correct? no envs in the names?
simple_eks_helm_release_name_prod: docket-track-infra # TODO: is this really correct? no envs in the names?

ci_simple_eks_helm_set_values:
  environment: ci
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::911277578290:role/a208084-ras-notifications-docket-track-ci-use1
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com # TODO: might need to vet this works for what ever it is for ras2?
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

int_simple_eks_helm_set_values:
  environment: int
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::911277578290:role/a208084-ras-notifications-docket-track-int-use1
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com # TODO: might need to vet this works for what ever it is for ras2?
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

qa_simple_eks_helm_set_values:
  environment: qa
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::911277578290:role/a208084-ras-notifications-docket-track-qa-use1
  public_hosted_zone_name: plexus-ras2-ppuse1.5771.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-ppuse1.5771.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com # TODO: might need to vet this works for what ever it is for ras2?
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2

prod_simple_eks_helm_set_values:
  environment: prod
  regionDD: us-east-1
  service_iam_role: arn:aws:iam::196165776688:role/a208084-ras-notifications-docket-track-prod-use1
  public_hosted_zone_name: plexus-ras2-use1.3772.aws.thomsonreuters.com
  private_hosted_zone_name: plexus-ras2-use1.3772.aws-int.thomsonreuters.com
  private_bigip_zone_name: ras.int.thomsonreuters.com # TODO: might need to vet this works for what ever it is for ras2?
  region_short: use1
  region_long: us-east-1
  cluster_short: ras2