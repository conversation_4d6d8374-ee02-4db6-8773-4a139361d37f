# ras-shared_pipeline-parameters

### Are you trying to deploy a pipeline for your microservice?
All you need to do is add (or modify) a config under the correct asset folder.  
Once you get your PR merged, the c5s pipeline in AWS will do the deployments.  
You no longer need to deploy the pipeline manually from your local machine.

### Curious if you have your config set up correctly before merging to main?
Navigate to the latest [merge util repo](https://github.com/tr/cars-shared_cumulus-template-replace-action) and read the **README.md** file.

### Looking for documentation on the CICD process via Cumulus?
Check out the Helix Wiki page: [CICD Process Via Cumulus](https://helix.thomsonreuters.com/static-sites/site-builds/ras_documentation/ras-documentation/16_cicd_via_cumulus/intro.html)

### What version of cumulus are we using?
- [a207891 - RAS Search - Cumulus Version](https://github.com/tr/ras-shared_pipeline-parameters/blob/main/project-configs/a207891-search/shared-variables-c5s-pipeline.yaml#L16)
- [a207915 - RAS Content Display - Cumulus Version](https://github.com/tr/ras-shared_pipeline-parameters/blob/main/project-configs/a207915-content-display/shared-variables-c5s-pipeline.yaml#L16)
- [a208084 - RAS Notifications - Cumulus Version](https://github.com/tr/ras-shared_pipeline-parameters/blob/main/project-configs/a208084-notifications/shared-variables-c5s-pipeline.yaml#L16)
- [a208203 - RAS Events - Cumulus Version](https://github.com/tr/ras-shared_pipeline-parameters/blob/main/project-configs/a208203-events/shared-variables-c5s-pipeline.yaml#L16)
